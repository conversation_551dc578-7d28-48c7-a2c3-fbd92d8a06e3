import { RcFile } from 'antd/es/upload';
import { useCallback } from 'react';
import { apiUrls, appInstance } from 'shared/api';
import { IS_ZT_BUILD } from 'shared/config';
import { asyncDownloadFile, downloadFile } from 'shared/lib';
import { normalizeFileName } from 'shared/model';

const ZT_UPLOAD = `${window.location.origin}/zuul/kzid_rest/${apiUrls.permission.excel.import}`;

type HandleRoleExport = (roleId: string) => void;
type HandleRoleImport = (file: RcFile, roleId: string) => Promise<void>;

export const useControlExcelActions = (): [
  HandleRoleExport,
  HandleRoleImport,
] => {
  const handleRoleExport = useCallback<HandleRoleExport>(
    (roleId: string): void => {
      asyncDownloadFile(
        appInstance
          .post<Blob>(apiUrls.permission.excel.export, null, {
            params: {
              roleId,
            },
            responseType: 'blob',
          })
          .then((res) =>
            downloadFile(res.data, normalizeFileName(res.headers)),
          ),
        {
          notice: 'Отправлен запрос на формирование шаблона',
          success: 'Шаблон успешно загружен',
          error: 'Ошибка формирования отчета',
        },
      );
    },
    [],
  );

  const handleRoleImport = useCallback<HandleRoleImport>(
    async (file: RcFile, roleId: string): Promise<void> => {
      const form = new FormData();
      form.append('file', file);

      await asyncDownloadFile(
        appInstance.post(
          IS_ZT_BUILD ? ZT_UPLOAD : apiUrls.permission.excel.import,
          form,
          {
            params: {
              roleId,
            },
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        ),
      );
    },
    [],
  );

  return [handleRoleExport, handleRoleImport];
};
