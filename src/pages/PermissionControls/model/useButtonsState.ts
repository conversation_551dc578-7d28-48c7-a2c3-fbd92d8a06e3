import { useEffect, useState } from 'react';

export const useButtonsState = (
  selectedRows: TableColumnsAndRows['rows'],
  checkboxStatus: string,
): typeof isButtonDisabled => {
  const [isButtonDisabled, setIsButtonDisabled] = useState<{
    delete: boolean;
    reset: boolean;
  }>({ reset: true, delete: true });

  useEffect(() => {
    if (selectedRows.length === 0) {
      setIsButtonDisabled({ reset: true, delete: true });
      return;
    }

    const isDeleted = selectedRows.filter(
      (item) => item?.checkboxStatus?.[checkboxStatus],
    );

    setIsButtonDisabled({
      reset: isDeleted.length !== selectedRows.length,
      delete: isDeleted.length > 0,
    });
  }, [checkboxStatus, selectedRows]);

  return isButtonDisabled;
};
