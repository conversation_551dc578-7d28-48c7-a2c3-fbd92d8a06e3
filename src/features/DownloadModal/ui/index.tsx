import { Checkbox, Radio, Space } from 'antd';
import { FC, useMemo } from 'react';

import { specialPermissionsStore } from 'entities/SpecialPermissions';
import { renderPopup } from 'shared/model';
import { ButtonsContainer, InputRow } from 'shared/ui';

import type { DownloadModalProps } from '..';
import { reportTypes, reportSelects } from '../config';
import { hooks } from '../store';
import { Export } from './export';
import { Report } from './report';

import styles from './styles.module.scss';

const DownloadModal: FC<DownloadModalProps> = ({
  permissions,
  isExportToExcel = false,
  onOutputOptions,
  url,
  reportControlDTO,
  additionalRequestParams,
  isLazyReport = false,
  reportEndpoint,
}) => {
  const [inputs, { setFormat, setHighlight, setReportType }] =
    hooks.useDownloadInputs();

  const downloadReport = hooks.useDownloadReport(
    reportEndpoint || url,
    isLazyReport,
    onOutputOptions,
    reportControlDTO,
    additionalRequestParams,
  );
  const downloadExcel = hooks.useExport(url, isExportToExcel, reportControlDTO);
  const selectKey = (url ? url.replaceAll('/', '') : null) as
    | keyof typeof reportSelects;
  const reportNameKey = reportSelects[selectKey][inputs.format - 1].typeNameKey;

  const hasRights = useMemo(
    () =>
      permissions.includes(
        reportNameKey as specialPermissionsStore.enums.ReportPermissions,
      ),
    [permissions, reportNameKey],
  );

  return (
    <Space direction="vertical" className="w-100">
      {isExportToExcel && <Export onClick={downloadExcel} />}

      <div className={styles.container}>
        <Report onChange={setFormat} selectValue={inputs.format} url={url} />
        <InputRow title="Подсветить строки">
          <Checkbox
            checked={inputs.isHighlighted}
            onChange={({ target: { checked } }) => setHighlight(checked)}
          />
        </InputRow>
        <InputRow title="Формат отчета">
          <Radio.Group
            className={styles.radio}
            value={inputs.reportType}
            onChange={({ target: { value } }) => {
              setReportType(value);
            }}
          >
            {Object.entries(reportTypes).map(([key, value]) => (
              <Radio value={key.toLowerCase()} key={key}>
                {value}
              </Radio>
            ))}
          </Radio.Group>
        </InputRow>
        <ButtonsContainer
          buttons={[
            {
              tooltip: !hasRights
                ? 'Нет прав для вывода отчета в файл'
                : undefined,
              disabled: !hasRights,
              title: 'Вывести в файл',
              key: 'output-to-file',
              type: 'primary',
              id: 'bc8a536e-8486-4597-ab3d-64f07fa07a5f',
              onClick: () => {
                downloadReport(inputs);
              },
            },
          ]}
        />
      </div>
    </Space>
  );
};

export const createDownloadModal = (props: DownloadModalProps): void => {
  const options = {
    title: props.title || 'Скачать в файл',
    popupUI: () => (
      <div className={styles.popup}>
        <DownloadModal {...props} />
      </div>
    ),
  };

  renderPopup(options);
};
