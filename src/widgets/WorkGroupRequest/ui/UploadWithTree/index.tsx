import { PlusOutlined } from '@ant-design/icons';
import { Button, Empty, Tree, Upload, notification } from 'antd';
import axios from 'axios';
import classNames from 'classnames';
import { FC, useEffect, useState } from 'react';
import {
  WorkGroupRequestConfig,
  WorkGroupRequestStore,
} from 'widgets/WorkGroupRequest';
import type { Files } from 'widgets/WorkGroupRequest';

import { DataGrid, NestedTabsWithTable, TableRowData } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import {
  createBasicClosableNotice,
  renderTreeTitle,
  useAppSelector,
  useAxiosRequest,
  useCreateSliceActions,
} from 'shared/model';
import { ApiContainer } from 'shared/ui';
import { BorderedFieldset } from 'shared/ui/BorderedFieldset';

import styles from './styles.module.scss';

interface UploadWithTreeProps {
  additionalParams: Partial<TableRowData>;
  cabinetId: string;
  isDisabled: boolean;
}

export const UploadWithTree: FC<UploadWithTreeProps> = ({
  isDisabled,
  additionalParams,
  cabinetId,
}) => {
  const [selectedKeys, setSelectedKeys] = useState<Key[]>([]);
  const [filesTrigger, filesState] = useAxiosRequest<TableColumnsAndRows>();
  const [filesTable, setFilesTable] = useState<TableColumnsAndRows>();
  const { id: itemId, isRequest, nestedTable } = additionalParams || {};
  useEffect(() => {
    let controller: AbortController | undefined;

    const nestedTabs = (nestedTable as NestedTabsWithTable)?.tabs;

    const filesTableFromNestedRow = nestedTabs?.find((tab) =>
      WorkGroupRequestConfig.constants.OUTPUT_FILES_ENDPOINTS.includes(
        tab.endpoint,
      ),
    )?.tableData;
    if (filesTableFromNestedRow?.columns) {
      filesTableFromNestedRow.columns =
        filesTableFromNestedRow?.columns?.filter(
          (data) => data?.dataIndex === 'name',
        );
    }

    if (itemId && isRequest && !filesTableFromNestedRow?.rows) {
      controller = new AbortController();
      const getFiles = async (): Promise<void> => {
        try {
          const res = await filesTrigger(
            generateUrlWithQueryParams(apiUrls.workGroup.request.outputFiles, {
              requestId: itemId,
              pageSize: 10,
              pageNumber: 1,
              light: true,
            }),
            { signal: controller?.signal },
          );

          setFilesTable(res);
        } catch (err) {
          if (axios.isAxiosError(err)) {
            appErrorNotification(
              'Произошла ошибка загрузки таблицы исходящих файлов',
              err as AppError,
            );
          }
        }
      };

      getFiles();
    } else {
      setFilesTable(filesTableFromNestedRow);
    }
    return () => {
      controller?.abort();
    };
  }, [isRequest, nestedTable, filesTrigger, itemId]);

  const bindedFileNames =
    filesTable?.rows.map((row) => row.name as string) || [];

  const { handleUpdateFileList, handleFileTreeSelect } = useCreateSliceActions(
    WorkGroupRequestStore.reducers.slice.actions,
  );

  const { fileList, fileTree, isPrepared } = useAppSelector(
    WorkGroupRequestStore.selectors.requestDataSelector,
  );

  const treeParams = WorkGroupRequestStore.hooks.useUploadTree(cabinetId);

  return (
    <div className={styles.container}>
      <BorderedFieldset
        title="Прикрепить ранее загруженные файлы"
        containerClassName={styles.containerTree}
      >
        <ApiContainer isPending={fileTree.isPending} error={fileTree.isError}>
          <Tree
            disabled={isDisabled}
            treeData={fileTree.data}
            loadData={treeParams.onLoadData}
            loadedKeys={treeParams.loadedKeys}
            className={styles.containerTreeElement}
            selectable
            selectedKeys={selectedKeys}
            showLine
            titleRender={renderTreeTitle}
            onSelect={(keys, element) => {
              if (
                element.nativeEvent.detail === 2 &&
                !element.node.isDirectory &&
                !isPrepared
              ) {
                setSelectedKeys(keys);
                if (
                  fileList.some(
                    (i) =>
                      i.uid === element.node.itemId ||
                      i.name === element.node.title,
                  ) ||
                  bindedFileNames.some((name) => name === element.node.title)
                ) {
                  notification.warn({
                    message:
                      'Файл с таким именем уже есть в списке или привязан к заявке',
                  });
                } else {
                  handleFileTreeSelect({
                    name: element.node.title,
                    uid: element.node.itemId,
                    editable: true,
                  } as Files);
                }

                setSelectedKeys([]);
              }
            }}
          />
        </ApiContainer>
      </BorderedFieldset>

      <BorderedFieldset
        title="Загрузить файлы"
        containerClassName={styles.uploader}
      >
        <Upload
          fileList={fileList}
          className={styles.uploaderAction}
          maxCount={10}
          multiple
          openFileDialogOnClick={!isPrepared && !isDisabled}
          onRemove={(file) => {
            if (!isPrepared) {
              const index = fileList.indexOf(file as Files);
              const newFileList = fileList.slice();
              newFileList.splice(index, 1);
              handleUpdateFileList(newFileList);
            }
          }}
          beforeUpload={(file, files) => {
            const isFirstFile = files[0].name === file.name;
            if (!isFirstFile) {
              return false;
            }

            const duplicateFileNames = files
              .filter((newFile) =>
                fileList.some(
                  (existingFile) => existingFile.name === newFile.name,
                ),
              )
              .map((el) => el.name)
              .concat(
                files
                  .filter(({ name }) =>
                    bindedFileNames.some((fileName) => name === fileName),
                  )
                  .map((el) => el.name),
              );

            const newFiles = files.filter(
              (newFile) =>
                !fileList.some(
                  (existingFile) => existingFile.name === newFile.name,
                ) && !bindedFileNames.some((name) => name === newFile.name),
            );

            if (duplicateFileNames.length > 0 && isFirstFile) {
              createBasicClosableNotice({
                message: 'Невозможно прикрепить файлы с одинаковыми именами',
                description: `Следующие файлы уже есть в списке или привязаны к заявке: ${duplicateFileNames.join(
                  ', ',
                )}`,
              });
            }

            if (newFiles.length > 0) {
              handleUpdateFileList([...fileList, ...newFiles]);
            }

            return false;
          }}
        >
          <Button icon={<PlusOutlined />} disabled={isPrepared || isDisabled}>
            Прикрепить файлы с этого компьютера
          </Button>
          <div className={classNames(styles.uploaderItems)}>
            {fileList.length === 0 && (
              <Empty
                className={styles.uploaderActionEmpty}
                description="Файлы для загрузки не прикреплены"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </div>
        </Upload>
      </BorderedFieldset>

      <ApiContainer error={filesState.error} isPending={filesState.isPending}>
        <BorderedFieldset title="Исходящие файлы заявки">
          <div className={styles.fileList}>
            <DataGrid
              columns={filesTable?.columns || []}
              rows={filesTable?.rows || []}
            />
          </div>
        </BorderedFieldset>
      </ApiContainer>
    </div>
  );
};
