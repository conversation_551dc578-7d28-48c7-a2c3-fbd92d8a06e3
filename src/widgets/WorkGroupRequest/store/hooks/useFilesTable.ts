import axios from 'axios';
import { useEffect, useMemo, useState } from 'react';
import { WorkGroupRequestConfig } from 'widgets/WorkGroupRequest';
import { NestedTabsWithTable, TableRowData } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

interface UseFilesTableProps {
  additionalParams: Partial<TableRowData>;
}

interface UseFilesTableReturn {
  bindedFileNames: string[];
  filesState: ReturnType<typeof useAxiosRequest>[1];
  filesTable: TableColumnsAndRows | undefined;
  filesTableFilteredColumns: TableColumnsAndRows['columns'] | undefined;
  filesTableFromNestedRow: TableColumnsAndRows | undefined;
}

export const useFilesTable = ({
  additionalParams,
}: UseFilesTableProps): UseFilesTableReturn => {
  const [filesTrigger, filesState] = useAxiosRequest<TableColumnsAndRows>();
  const [filesTable, setFilesTable] = useState<TableColumnsAndRows>();
  const { id: itemId, isRequest, nestedTable } = additionalParams || {};
  const nestedTabs = (nestedTable as NestedTabsWithTable)?.tabs;

  const filesTableFromNestedRow = useMemo(
    () =>
      nestedTabs?.find((tab) =>
        WorkGroupRequestConfig.constants.OUTPUT_FILES_ENDPOINTS.includes(
          tab.endpoint,
        ),
      )?.tableData,
    [nestedTabs],
  );

  const filesTableFilteredColumns = filesTableFromNestedRow?.columns?.filter(
    (data) => data?.dataIndex === 'name',
  );

  useEffect(() => {
    if (!itemId || !isRequest || filesTableFromNestedRow?.rows) {
      return undefined;
    }

    const controller = new AbortController();

    const getFiles = async (): Promise<void> => {
      try {
        const res = await filesTrigger(
          generateUrlWithQueryParams(apiUrls.workGroup.request.outputFiles, {
            requestId: itemId,
            pageSize: 10,
            pageNumber: 1,
            light: true,
          }),
          { signal: controller.signal },
        );

        setFilesTable(res);
      } catch (err) {
        if (axios.isAxiosError(err) && !controller.signal.aborted) {
          appErrorNotification(
            'Произошла ошибка загрузки таблицы исходящих файлов',
            err as AppError,
          );
        }
      }
    };

    getFiles();

    return () => {
      controller.abort();
    };
  }, [itemId, isRequest, filesTableFromNestedRow?.rows, filesTrigger]);

  const bindedFileNames = useMemo(
    () => filesTable?.rows.map((row) => row.name as string) || [],
    [filesTable?.rows],
  );

  return {
    filesState,
    filesTable,
    filesTableFromNestedRow,
    filesTableFilteredColumns,
    bindedFileNames,
  };
};