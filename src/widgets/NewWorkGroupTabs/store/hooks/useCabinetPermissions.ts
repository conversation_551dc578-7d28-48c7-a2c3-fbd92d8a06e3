import { useLayoutEffect, useState } from 'react';
import { PermissionsInitial, permissionsStore } from 'entities/Permissions';
import {
  SpecialPermissionsStatus,
  specialPermissionsStore,
} from 'entities/SpecialPermissions';
import { apiUrls } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import { useAppDispatch, useAppSelector, useAxiosRequest } from 'shared/model';

export const useCabinetPermissions = (
  cabinetId: string,
): [
  typeof permissionsStatuses,
  PermissionsInitial,
  SpecialPermissionsStatus,
  isFullFileAccess: boolean,
] => {
  const dispatch = useAppDispatch();
  const [trigger, permissionsStatuses] =
    useAxiosRequest<Partial<PermissionsInitial>>();

  const [permissions, setPermissions] = useState<PermissionsInitial>(
    permissionsStore.reducers.slice.getInitialState,
  );

  const getAbacPermissions = async (): Promise<void> => {
    await trigger(
      generateUrlWithQueryParams(apiUrls.workGroup.permissions, { cabinetId }),
    ).then((res) => setPermissions({ ...permissions, ...res }));
  };

  const wildcardPermissions = useAppSelector(
    specialPermissionsStore.selectors.wildcardPermissionsSelector,
  );
  const isFullFilesAccess = useAppSelector(
    specialPermissionsStore.selectors.isFullFilesAccessSelector,
  );

  useLayoutEffect(() => {
    dispatch(specialPermissionsStore.thunks.getWildcardPermissionsThunk());
    getAbacPermissions();
  }, [cabinetId]); // eslint-disable-line

  return [
    permissionsStatuses,
    permissions,
    wildcardPermissions.status,
    isFullFilesAccess,
  ];
};
